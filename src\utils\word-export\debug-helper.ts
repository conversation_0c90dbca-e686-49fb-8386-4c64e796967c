/**
 * 调试辅助工具
 * 用于保存中间处理结果，便于调试和验证
 */
import * as fs from 'fs';
import * as path from 'path';

/**
 * 保存预处理后的HTML到文件
 * @param htmlContent 预处理后的HTML内容
 * @param outputPath 输出文件路径（可选，默认保存到testFiles目录）
 */
export function saveProcessedHtml(
  htmlContent: string,
  outputPath?: string
): string {
  try {
    const defaultPath = path.join(
      process.cwd(),
      'testFiles',
      'processed-html-preview.html'
    );

    const filePath = outputPath || defaultPath;

    // 确保目录存在
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    // 保存文件
    fs.writeFileSync(filePath, htmlContent, 'utf-8');

    console.log(`预处理后的HTML已保存到: ${filePath}`);
    return filePath;
  } catch (error) {
    console.error('保存预处理HTML失败:', error);
    throw error;
  }
}

/**
 * 输出转换过程的关键信息
 * @param stage 转换阶段
 * @param info 信息内容
 */
export function logConversionInfo(stage: string, info: any): void {
  console.log(`[${stage}] ${JSON.stringify(info, null, 2)}`);
}
