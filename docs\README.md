# 📚 项目文档目录

本目录包含了Word试题解析服务的完整技术文档。

## 📋 文档结构

### 🔧 核心功能文档
- [`Word试题导入功能完整说明.md`](./Word试题导入功能完整说明.md) - Word文档解析功能的详细说明
- [`HTML转Word着重号功能完整说明.md`](./HTML转Word着重号功能完整说明.md) - HTML转Word功能完整实现，特别支持着重号处理
- [`文件解析队列使用说明.md`](./文件解析队列使用说明.md) - 异步文件处理队列的使用指南

### 🤖 AI服务集成
- [`NLP服务集成说明.md`](./NLP服务集成说明.md) - 自然语言处理服务的集成和使用

## 📖 快速导航

### 🚀 快速开始
如果你是第一次使用本项目，建议按以下顺序阅读文档：

1. **了解Word解析功能** → [`Word试题导入功能完整说明.md`](./Word试题导入功能完整说明.md)
2. **了解HTML转Word功能** → [`HTML转Word着重号功能完整说明.md`](./HTML转Word着重号功能完整说明.md)
3. **了解队列处理机制** → [`文件解析队列使用说明.md`](./文件解析队列使用说明.md)
4. **了解AI服务集成** → [`NLP服务集成说明.md`](./NLP服务集成说明.md)

### 🔍 按功能查找

#### Word文档处理
- **Word解析** → [`Word试题导入功能完整说明.md`](./Word试题导入功能完整说明.md)
  - 支持复杂试题结构解析
  - 题型自动识别（选择题、判断题、填空题等）
  - 格式保持和样式转换
  - 生成JSON结果和HTML预览

- **HTML转Word** → [`HTML转Word着重号功能完整说明.md`](./HTML转Word着重号功能完整说明.md)
  - 三步处理流程：预处理 → 基础转换 → 特殊样式处理
  - 完美支持着重号（文字下方小点）
  - 保持所有HTML格式完整转换
  - 简洁API，自动检测特殊样式

#### 系统集成
- **异步处理队列** → [`文件解析队列使用说明.md`](./文件解析队列使用说明.md)
- **NLP服务集成** → [`NLP服务集成说明.md`](./NLP服务集成说明.md)

## 🛠️ 技术栈说明

### 核心技术
- **Node.js + TypeScript** - 主要开发语言和运行环境
- **Midway.js** - 企业级Node.js框架
- **mammoth.js** - Word文档解析库
- **html-to-docx** - HTML转Word基础转换
- **xmldom** - XML处理和Word文档补丁

### 文档处理架构
- **Word解析**: 使用mammoth.js解析.docx文件，支持复杂格式
- **HTML生成**: 支持完整的HTML格式输出，包含样式和结构
- **Word导出**: 三步处理流程，支持着重号等特殊样式
  1. HTML预处理：优化结构，转换特殊标记
  2. html-to-docx：处理常规格式
  3. XML补丁：处理特殊样式

### 特色功能
- **着重号支持**: 完美支持Word原生着重号效果
- **格式保持**: 表格、段落、列表等格式完整转换
- **智能检测**: 自动检测特殊样式需求
- **可扩展架构**: 易于添加新的特殊样式处理

## 🎯 功能亮点

### HTML转Word着重号功能
- ✅ **完美效果**: 文字下方显示小点，符合Word标准
- ✅ **自动检测**: 支持多种着重号标记格式
- ✅ **格式保持**: 所有其他格式完整不变
- ✅ **简洁API**: 无需复杂配置，自动处理
- ✅ **可扩展**: 易于添加其他特殊样式支持

### 测试验证
- 使用标准测试文件 `testFiles/input-html-sample.html`
- 生成结果文件 `testFiles/output-word-result.docx`
- 包含3个着重号测试用例
- 验证表格、段落、字体等完整格式

## 📝 文档维护

### 更新说明
- 文档已整理合并，删除过程性文档
- 保留核心功能的完整说明
- 所有文档与当前实现保持同步

### 文档结构优化
- 删除了14个过程性文档
- 合并为4个核心功能文档
- 保持文档的准确性和实用性

---

💡 **提示**: HTML转Word着重号功能已完全实现并测试通过，可直接用于生产环境。如需了解详细实现，请查阅 [`HTML转Word着重号功能完整说明.md`](./HTML转Word着重号功能完整说明.md)。
- ✅ 更新日期至2025-05-19
- ✅ 添加项目状态说明
- ✅ 更新实现进度表，反映当前完成状态

### 🎯 文档结构优化

```
docs/
├── README.md                           ← 📚 本文档（文档导航）
├── HTML转Word功能说明.md                ← 🔧 功能使用指南
├── NLP服务集成说明.md                   ← 🤖 AI功能集成
├── 文件解析队列使用说明.md               ← ⚡ 队列系统指南
└── Word试题导入工具开发设计文档.md       ← 📋 项目设计文档
```

## 🚀 快速导航

- **想了解如何使用HTML转Word功能？** → [HTML转Word功能说明.md](./HTML转Word功能说明.md)
- **想集成NLP智能分析功能？** → [NLP服务集成说明.md](./NLP服务集成说明.md)
- **想了解队列系统配置？** → [文件解析队列使用说明.md](./文件解析队列使用说明.md)
- **想了解项目整体设计？** → [Word试题导入工具开发设计文档.md](./Word试题导入工具开发设计文档.md)

## 📖 与主README的关系

- **主README**: 面向用户的快速上手指南
- **docs目录**: 面向开发者的详细技术文档

建议用户先阅读主README进行快速体验，然后根据需要查阅docs目录中的具体技术文档。
