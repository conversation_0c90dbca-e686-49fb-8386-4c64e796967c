/**
 * Word文档后处理工具
 * 处理html-to-docx转换后需要进一步优化的内容
 */
import { Document, Packer, Paragraph, TextRun } from 'docx';

export interface PostprocessOptions {
  /** 是否处理着重号 */
  processEmphasisMarks?: boolean;
  /** 是否优化字体设置 */
  optimizeFonts?: boolean;
  /** 是否调整段落间距 */
  adjustParagraphSpacing?: boolean;
  /** 原始HTML内容，用于提取着重号信息 */
  originalHtmlContent?: string;
}

/**
 * Word文档后处理器
 */
export class WordPostprocessor {
  private docxBuffer: Buffer;

  constructor(docxBuffer: Buffer) {
    this.docxBuffer = docxBuffer;
  }

  /**
   * 执行后处理
   */
  async postprocess(options: PostprocessOptions = {}): Promise<Buffer> {
    const {
      processEmphasisMarks = true,
      optimizeFonts = true,
      adjustParagraphSpacing = true,
      originalHtmlContent,
    } = options;

    console.log('开始Word文档后处理...');

    if (processEmphasisMarks && originalHtmlContent) {
      // 使用docx库处理着重号
      console.log('使用docx库处理着重号...');
      return await this.processEmphasisWithDocx(originalHtmlContent);
    }

    if (optimizeFonts) {
      // 字体优化：输出提示信息
      console.log('字体优化：html-to-docx已处理基本字体样式');
    }

    if (adjustParagraphSpacing) {
      // 段落间距调整：输出提示信息
      console.log('段落间距调整：html-to-docx已处理基本段落格式');
    }

    console.log('Word文档后处理完成');
    return this.docxBuffer;
  }

  /**
   * 使用docx库处理着重号
   * 修改版本：在html-to-docx基础上补充处理着重号，而不是完全重建
   */
  private async processEmphasisWithDocx(originalHtmlContent: string): Promise<Buffer> {
    try {
      console.log('检测到着重号处理需求，但当前实现会破坏其他格式');
      console.log('暂时回退到html-to-docx结果，保持其他格式完整');

      // 暂时回退到原始方案，避免破坏其他格式
      // TODO: 实现真正的补充处理，而不是完全重建
      return this.docxBuffer;

    } catch (error) {
      console.error('处理着重号失败:', error);
      console.log('回退到原始Word文档');
      return this.docxBuffer;
    }
  }





  /**
   * 使用docx库创建增强的Word文档
   * 这是一个备用方案，当html-to-docx无法满足需求时使用
   */
  async createEnhancedDocument(
    htmlContent: string,
    options: any = {}
  ): Promise<Buffer> {
    console.log('使用docx库创建增强Word文档...');

    // 创建新的Word文档
    const doc = new Document({
      sections: [
        {
          properties: {},
          children: [
            new Paragraph({
              children: [
                new TextRun({
                  text: '这是使用docx库创建的文档',
                  bold: true,
                }),
              ],
            }),
            new Paragraph({
              children: [
                new TextRun({
                  text: '当html-to-docx无法满足特殊需求时，可以使用此方法',
                }),
              ],
            }),
          ],
        },
      ],
    });

    // 生成文档Buffer
    const buffer = await Packer.toBuffer(doc);
    return buffer;
  }
}

/**
 * 后处理Word文档
 * @param docxBuffer 原始Word文档Buffer
 * @param options 后处理选项
 * @returns 后处理后的Word文档Buffer
 */
export async function postprocessWord(
  docxBuffer: Buffer,
  options: PostprocessOptions = {}
): Promise<Buffer> {
  const postprocessor = new WordPostprocessor(docxBuffer);
  return await postprocessor.postprocess(options);
}
