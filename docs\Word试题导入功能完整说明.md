# Word试题导入功能完整说明

## 🎯 功能概述

Word试题导入功能支持解析Word文档（.docx格式），自动提取试题内容，包括题型识别、题干提取、选项解析、答案识别等，并生成结构化的JSON数据和可视化HTML预览。

## 🏗️ 技术架构

### 核心技术栈
- **mammoth.js** - Word文档解析引擎
- **正则表达式** - 题型和结构识别
- **HTML生成** - 可视化预览
- **JSON输出** - 结构化数据存储

### 处理流程
```
Word文档输入（.docx）
    ↓
mammoth.js解析为HTML
    ↓
HTML内容分析和清理
    ↓
题型识别和结构分析
    ↓
内容提取和格式化
    ↓
生成JSON结果 + HTML预览
```

## 🚀 使用方法

### 基础使用
```typescript
import { WordParserService } from './src/service/word-parser.service';

const wordParserService = new WordParserService();

// 解析Word文档
const result = await wordParserService.parseWordFile('path/to/document.docx');
console.log(result); // 返回解析结果
```

### 命令行测试
```bash
# 解析Word文档
node --require ts-node/register src/example/word-parser-example.ts

# 预期输出
# - input-word-sample_parsed.json (解析结果)
# - input-word-sample_preview.html (预览文件)
# - 控制台输出解析统计
```

### API接口使用
```bash
# 启动服务器
npm run dev

# 测试Word解析API
curl -X POST http://localhost:3131/word/parse \
  -H "Content-Type: application/json" \
  -d '{"filePath": "testFiles/input-word-sample.docx"}'

# 测试文件上传API
curl -X POST http://localhost:3131/word/upload \
  -F "file=@testFiles/input-word-sample.docx"
```

## 📊 支持的题型

### 自动识别的题型
- **选择题**：单选题、多选题
- **判断题**：对错题、是非题
- **填空题**：单空、多空
- **简答题**：主观题
- **阅读理解**：包含材料和问题

### 题型识别规则
```typescript
// 选择题识别
/^\s*\d+\.\s*.+（\s*）/  // 1. 题干（　　）
/^[A-D]\./               // A. 选项

// 判断题识别
/判断|对错|是否|正确/      // 关键词识别

// 填空题识别
/_{2,}|（\s*）/          // 下划线或空括号
```

## 📝 解析结果格式

### JSON输出结构
```json
{
  "questions": [
    {
      "id": 1,
      "type": "选择题",
      "question": "题干内容",
      "options": [
        {"A": "选项A内容"},
        {"B": "选项B内容"},
        {"C": "选项C内容"},
        {"D": "选项D内容"}
      ],
      "answer": "C",
      "explanation": "解析内容",
      "difficulty": "中等",
      "knowledgePoints": ["知识点1", "知识点2"],
      "page": 1
    }
  ],
  "metadata": {
    "totalQuestions": 8,
    "questionTypes": {
      "选择题": 6,
      "阅读理解": 2
    },
    "parseTime": "2025-01-01T12:00:00Z"
  }
}
```

### HTML预览特点
- **可视化展示**：友好的界面显示解析结果
- **格式保持**：保持原Word文档的基本格式
- **错误标记**：高亮显示可能的解析错误
- **统计信息**：显示题目数量、类型分布等

## 🔧 核心功能详解

### Word文档解析
```typescript
// 使用mammoth.js解析Word文档
const result = await mammoth.convertToHtml({path: filePath});
const htmlContent = result.value;

// 清理和标准化HTML
const cleanedHtml = this.cleanHtml(htmlContent);

// 分析文档结构
const structure = this.analyzeDocumentStructure(cleanedHtml);
```

### 题型识别算法
```typescript
private identifyQuestionType(content: string): string {
  // 选择题识别
  if (/（\s*）/.test(content) && /[A-D]\./.test(content)) {
    return '选择题';
  }
  
  // 判断题识别
  if (/判断|对错|是否|正确/.test(content)) {
    return '判断题';
  }
  
  // 填空题识别
  if (/_{2,}|（\s*）/.test(content)) {
    return '填空题';
  }
  
  // 阅读理解识别
  if (/阅读|材料|短文/.test(content)) {
    return '阅读理解';
  }
  
  return '简答题';
}
```

### 内容提取处理
```typescript
private extractQuestionContent(element: Element): QuestionData {
  const questionText = this.extractQuestionText(element);
  const options = this.extractOptions(element);
  const answer = this.extractAnswer(element);
  const explanation = this.extractExplanation(element);
  
  return {
    question: questionText,
    options: options,
    answer: answer,
    explanation: explanation
  };
}
```

## 📊 质量保证

### 解析准确性
- **结构识别**：准确识别题目边界和层次结构
- **内容提取**：完整提取题干、选项、答案等
- **格式保持**：保持重要的格式信息
- **错误处理**：对异常情况进行适当处理

### 性能优化
- **内存管理**：大文件分块处理
- **缓存机制**：重复解析结果缓存
- **异步处理**：支持异步解析操作
- **错误恢复**：解析失败时的恢复机制

## 🎯 测试验证

### 测试文件
- **input-word-sample.docx**：包含8道题目的标准测试文件
- **input-word-sample_parsed.json**：解析结果JSON文件
- **input-word-sample_preview.html**：可视化预览文件

### 验证标准
- ✅ 能够正确识别8道题目的题型
- ✅ 能够提取题干、选项、答案等完整内容
- ✅ 能够保持原始格式和样式
- ✅ 能够输出结构化JSON数据
- ✅ 生成可视化HTML预览文件

### 常见问题处理
```typescript
// 处理特殊字符
private normalizeText(text: string): string {
  return text
    .replace(/\u00A0/g, ' ')  // 替换不间断空格
    .replace(/\s+/g, ' ')     // 合并多个空格
    .trim();                  // 去除首尾空格
}

// 处理表格内容
private extractTableContent(table: Element): any[] {
  const rows = table.querySelectorAll('tr');
  return Array.from(rows).map(row => {
    const cells = row.querySelectorAll('td, th');
    return Array.from(cells).map(cell => cell.textContent?.trim() || '');
  });
}
```

## 🔄 扩展功能

### NLP增强（可选）
- **智能分类**：使用AI模型进行题型分类
- **知识点提取**：自动识别题目涉及的知识点
- **相似题检测**：检测重复或相似的题目

### 批量处理
- **文件夹解析**：批量处理多个Word文件
- **进度跟踪**：显示批量处理进度
- **结果合并**：将多个文件的解析结果合并

### 数据导出
- **Excel导出**：将解析结果导出为Excel格式
- **数据库存储**：直接存储到数据库
- **API集成**：与其他系统进行数据交换

## 📁 相关文件

### 核心实现文件
- `src/service/word-parser.service.ts` - 主解析服务
- `src/utils/word-parser/` - 解析工具类
- `src/controller/word.controller.ts` - API控制器

### 测试文件
- `testFiles/input-word-sample.docx` - 测试源文件
- `testFiles/input-word-sample_parsed.json` - 解析结果
- `testFiles/input-word-sample_preview.html` - 预览文件

### 示例文件
- `src/example/word-parser-example.ts` - 使用示例

这是一个功能完整、技术成熟的Word试题解析解决方案，能够准确识别和提取各种类型的试题内容，为教育系统提供可靠的题库导入功能。
