# 📁 测试文件目录说明

本目录包含了Word试题解析服务的测试文件，用于验证Word解析和HTML转Word功能。

## 📋 文件结构

```
testFiles/
├── README.md                    ← 📚 本说明文件
├── input-word-sample.docx       ← 📄 Word解析测试源文件
├── input-word-sample_parsed.json ← 📊 Word解析结果文件
├── input-word-sample_preview.html ← 🔍 Word解析预览文件
├── input-html-sample.html       ← 🌐 HTML转Word测试源文件
├── html-template.html           ← 📝 HTML预览模板文件
└── output-word-result.docx      ← 📤 HTML转Word结果文件
```

## 📝 文件说明

### 🔤 测试源文件

#### 📄 `input-word-sample.docx`
- **用途**: Word文档解析功能的测试源文件
- **内容**: 包含8道试题的完整Word文档，含表格、段落、特殊格式等
- **测试功能**:
  - Word文档解析和内容提取
  - 题型识别（选择题、阅读理解等）
  - 格式保持和样式转换
  - 结构化数据输出

#### 🌐 `input-html-sample.html`
- **用途**: HTML转Word功能的测试源文件
- **内容**: 包含着重号、表格、段落等复杂格式的HTML内容
- **特殊功能**:
  - 着重号处理（`data-emphasis-mark="dot"`）
  - 复杂表格结构转换
  - 多种字体和样式保持
  - 试题结构完整转换

### 🔍 解析结果文件

#### 📊 `input-word-sample_parsed.json`
- **用途**: Word解析的结构化结果
- **内容**: JSON格式的题目数据，包含题型、题干、选项、答案等
- **生成方式**: 由Word解析服务自动生成

#### 🔍 `input-word-sample_preview.html`
- **用途**: Word解析结果的可视化预览
- **内容**: 将解析结果转换为HTML格式，便于人工验证
- **功能**: 提供友好的预览界面，验证解析准确性

### 🛠️ 辅助文件

#### 📝 `html-template.html`
- **用途**: HTML预览的模板文件
- **作用**: 为解析结果提供完整的HTML结构框架
- **功能**:
  - 统一的样式管理
  - 响应式布局支持
  - 便于浏览器预览和打印

### 📤 转换结果文件

#### 📄 `output-word-result.docx`
- **用途**: HTML转Word功能的最终结果
- **生成方式**: 由 `input-html-sample.html` 通过三步处理流程生成
- **处理流程**:
  1. **HTML预处理**: 转换着重号为特殊标记，优化结构
  2. **html-to-docx转换**: 处理表格、段落、列表等常规格式
  3. **XML补丁处理**: 处理着重号等特殊样式
- **验证内容**:
  - 着重号效果（文字下方小点）
  - 表格结构和样式完整性
  - 字体、颜色、对齐等格式准确性
  - 试题结构和编号正确性

## 🚀 使用方法

### Word解析测试

```bash
# 解析Word文档（使用默认测试文件）
node --require ts-node/register src/example/word-parser-example.ts

# 或者指定特定的Word文件
node --require ts-node/register src/example/word-parser-example.ts path/to/your/document.docx

# 预期输出
# - input-word-sample_parsed.json (解析结果)
# - input-word-sample_preview.html (预览文件)
# - 控制台输出解析统计
```

### HTML转Word测试

```bash
# HTML转Word转换（包含着重号处理）
node --require ts-node/register src/example/word-export-example.ts

# 预期输出
# - output-word-result.docx (最终Word文档)
# - 控制台输出三步处理流程日志
```

### 完整功能测试

```bash
# 测试完整的HTML转Word流程
node --require ts-node/register -e "
const { WordExportService } = require('./src/service/word-export.service');
const fs = require('fs');
const path = require('path');

async function test() {
  const service = new WordExportService();
  const htmlContent = fs.readFileSync('testFiles/input-html-sample.html', 'utf-8');
  await service.exportHtmlToWordFile(htmlContent, 'testFiles/output-word-result.docx', {
    title: '2025年06月10日作业'
  });
  console.log('转换完成！');
}
test();
"
```

### API接口测试

```bash
# 启动服务器
npm run dev

# 测试Word解析API
curl -X POST http://localhost:3131/word/parse \
  -H "Content-Type: application/json" \
  -d '{"filePath": "testFiles/input-word-sample.docx"}'

# 测试文件上传API
curl -X POST http://localhost:3131/word/upload \
  -F "file=@testFiles/input-word-sample.docx"
```

## 🔄 文件更新说明

### 自动生成的文件

以下文件会在测试过程中自动生成或更新：

- `output-word-result.docx` - 每次运行HTML转Word测试时更新
- `input-word-sample_parsed.json` - Word解析结果（如果启用JSON输出）

### 手动维护的文件

以下文件需要手动维护和更新：

- `input-word-sample.docx` - 根据测试需要更新Word内容
- `input-html-sample.html` - 根据测试需要更新HTML内容
- `html-template.html` - 根据模板需要调整结构

## 📊 测试验证

### 成功标准

#### Word解析测试
- ✅ 能够正确识别8道题目的题型
- ✅ 能够提取题干、选项、答案等完整内容
- ✅ 能够保持原始格式和样式
- ✅ 能够输出结构化JSON数据
- ✅ 生成可视化HTML预览文件

#### HTML转Word测试（着重号功能）
- ✅ 生成的Word文档能正常打开
- ✅ 着重号效果正确（3个"新鲜感"显示文字下方小点）
- ✅ 表格结构和样式完整保持
- ✅ 字体、颜色、对齐等格式准确转换
- ✅ 试题编号和结构完整
- ✅ 无特殊标记显示（不显示[EMPHASIS]等）
- ✅ 文件大小合理（通常20-100KB）

#### 着重号处理验证
- ✅ 自动检测：识别`data-emphasis-mark="dot"`和CSS样式
- ✅ 三步处理：HTML预处理 → html-to-docx → XML补丁
- ✅ 纯净效果：只在文字下方添加小点，无额外样式
- ✅ 格式保持：其他所有格式完整不变

### 常见问题

#### 文件不存在错误
```bash
# 确保在项目根目录运行命令
pwd  # 应该显示项目根目录
ls testFiles/  # 应该能看到测试文件
```

#### 权限错误
```bash
# 检查文件权限
ls -la testFiles/
# 如果需要，修改权限
chmod 644 testFiles/*
```

## 🎯 扩展测试

### 添加新的测试文件

1. **添加Word测试文件**:
   ```bash
   cp your-test.docx testFiles/input-word-custom.docx
   ```

2. **添加HTML测试文件**:
   ```bash
   cp your-test.html testFiles/input-html-custom.html
   ```

3. **运行自定义测试**:
   ```bash
   node --require ts-node/register src/example/word-parser-example.ts testFiles/input-word-custom.docx
   ```

### 批量测试

```bash
# 测试所有Word文件
for file in testFiles/input-word-*.docx; do
  echo "测试文件: $file"
  node --require ts-node/register src/example/word-parser-example.ts "$file"
done
```

---

**💡 提示**: 建议在修改测试文件前先备份原始文件，以便随时恢复到已知的工作状态。
